#!/usr/bin/env python3
"""
PDF AcroForm 字段读取脚本
专门用于提取设置了 numberOfCells 属性的 comb 字段
"""

import json
import sys
from typing import Dict, List, Any, Optional
import PyPDF2
from PyPDF2.generic import DictionaryObject, ArrayObject, IndirectObject


class PDFAcroFormReader:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.reader = None
        self.acroform = None
        self.file_handle = None

    def open_pdf(self) -> bool:
        """打开PDF文件并获取AcroForm"""
        try:
            # 保持文件打开状态
            self.file_handle = open(self.pdf_path, 'rb')
            self.reader = PyPDF2.PdfReader(self.file_handle)

            # 获取AcroForm
            if '/AcroForm' in self.reader.trailer['/Root']:
                self.acroform = self.reader.trailer['/Root']['/AcroForm']
                return True
            else:
                print("PDF文件中没有找到AcroForm")
                return False

        except Exception as e:
            print(f"打开PDF文件时出错: {e}")
            return False

    def close_pdf(self):
        """关闭PDF文件"""
        if hasattr(self, 'file_handle') and self.file_handle:
            self.file_handle.close()

    def resolve_reference(self, obj: Any) -> Any:
        """解析间接引用对象"""
        if isinstance(obj, IndirectObject):
            return obj.get_object()
        return obj

    def extract_field_info(self, field_obj: DictionaryObject, parent_name: str = "") -> Dict[str, Any]:
        """提取字段信息"""
        field_info = {
            'name': '',
            'full_name': '',
            'type': '',
            'flags': 0,
            'numberOfCells': None,
            'has_comb': False,
            'raw_field': {}
        }

        # 解析字段对象
        field = self.resolve_reference(field_obj)
        if not isinstance(field, DictionaryObject):
            return field_info

        # 获取字段名称
        field_name = ""
        if '/T' in field:
            field_name = str(field['/T'])

        # 构建完整名称
        if parent_name:
            full_name = f"{parent_name}.{field_name}" if field_name else parent_name
        else:
            full_name = field_name

        field_info['name'] = field_name
        field_info['full_name'] = full_name

        # 获取字段类型
        if '/FT' in field:
            field_info['type'] = str(field['/FT'])

        # 获取字段标志
        if '/Ff' in field:
            flags = field['/Ff']
            if isinstance(flags, int):
                field_info['flags'] = flags
                # 检查是否设置了comb标志 (bit 24, 值为 0x1000000)
                field_info['has_comb'] = bool(flags & 0x1000000)

        # 查找numberOfCells属性 - 这通常在字段的附加属性中
        # 检查各种可能的位置
        for key in field.keys():
            if 'numberOfCells' in str(key) or 'MaxLen' in str(key):
                field_info['numberOfCells'] = field[key]
                break

        # 如果是文本字段且有comb标志，检查MaxLen
        if field_info['has_comb'] and '/MaxLen' in field:
            field_info['numberOfCells'] = field['/MaxLen']

        # 保存原始字段数据用于调试
        field_info['raw_field'] = {str(k): str(v) for k, v in field.items()}

        return field_info

    def process_field_tree(self, fields: Any, parent_name: str = "") -> List[Dict[str, Any]]:
        """递归处理字段树"""
        all_fields = []

        if not fields:
            return all_fields

        # 解析字段数组
        fields_array = self.resolve_reference(fields)
        if not isinstance(fields_array, ArrayObject):
            return all_fields

        for field_ref in fields_array:
            field_obj = self.resolve_reference(field_ref)
            if not isinstance(field_obj, DictionaryObject):
                continue

            # 提取当前字段信息
            field_info = self.extract_field_info(field_obj, parent_name)

            # 检查是否有子字段
            if '/Kids' in field_obj:
                # 这是一个父字段，递归处理子字段
                child_fields = self.process_field_tree(field_obj['/Kids'], field_info['full_name'])
                all_fields.extend(child_fields)
            else:
                # 这是一个叶子字段
                all_fields.append(field_info)

        return all_fields

    def find_comb_fields_with_cells(self, target_cells: int = 3) -> Dict[str, Any]:
        """查找设置了指定numberOfCells的comb字段"""
        if not self.open_pdf():
            return {
                "error": "无法打开PDF文件或找到AcroForm",
                "pdf_file": self.pdf_path,
                "total_fields_found": 0,
                "fields": []
            }

        result = {
            "pdf_file": self.pdf_path,
            "target_numberOfCells": target_cells,
            "total_fields_found": 0,
            "comb_fields_with_target_cells": 0,
            "fields": [],
            "all_fields_summary": []
        }

        try:
            # 获取字段数组
            if '/Fields' not in self.acroform:
                result["error"] = "AcroForm中没有找到字段"
                return result

            # 处理所有字段
            all_fields = self.process_field_tree(self.acroform['/Fields'])
            result["total_fields_found"] = len(all_fields)

            # 筛选符合条件的字段
            matching_fields = []
            for field in all_fields:
                # 添加到总结中
                result["all_fields_summary"].append({
                    "name": field['full_name'],
                    "type": field['type'],
                    "has_comb": field['has_comb'],
                    "numberOfCells": field['numberOfCells']
                })

                # 检查是否符合条件
                if (field['numberOfCells'] == target_cells or
                    (field['has_comb'] and field['numberOfCells'] == target_cells)):
                    matching_fields.append(field)

            result["fields"] = matching_fields
            result["comb_fields_with_target_cells"] = len(matching_fields)

        except Exception as e:
            result["error"] = f"处理字段时出错: {e}"

        return result


def main():
    """主函数"""
    pdf_path = "fw9.pdf"
    target_cells = 3

    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    if len(sys.argv) > 2:
        target_cells = int(sys.argv[2])

    print(f"正在分析PDF文件: {pdf_path}")
    print(f"查找numberOfCells={target_cells}的字段...")

    reader = PDFAcroFormReader(pdf_path)
    try:
        result = reader.find_comb_fields_with_cells(target_cells)

        # 输出结果到JSON文件
        output_file = "numberOfCells_fields.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    finally:
        reader.close_pdf()

    print(f"\n结果已保存到: {output_file}")

    # 打印摘要
    print(f"\n=== 分析摘要 ===")
    print(f"PDF文件: {result['pdf_file']}")
    print(f"总字段数: {result['total_fields_found']}")
    print(f"目标numberOfCells: {result['target_numberOfCells']}")
    print(f"符合条件的字段数: {result['comb_fields_with_target_cells']}")

    if result.get('error'):
        print(f"错误: {result['error']}")

    if result['fields']:
        print(f"\n=== 符合条件的字段 ===")
        for field in result['fields']:
            print(f"字段名: {field['full_name']}")
            print(f"类型: {field['type']}")
            print(f"numberOfCells: {field['numberOfCells']}")
            print(f"是否为comb字段: {field['has_comb']}")
            print("-" * 40)


if __name__ == "__main__":
    main()